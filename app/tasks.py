import os
import logging
import re
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from difflib import SequenceMatcher
from celery import current_task
from flask import current_app
from openai import OpenAI
from app import celery, db
from app.models import AdaptationTask
from app.utils import split_text_into_chapters, create_chapter_files

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BlockError(Exception):
    pass

def is_allowed_character(char: str) -> bool:
    """检查字符是否为允许的字符类型（数字、英文、中文）"""
    if not char or char.isspace():
        return True

    # 检查数字
    if char.isdigit():
        return True

    # 检查英文字母
    if 'a' <= char.lower() <= 'z':
        return True

    # 检查中文字符
    if '\u4e00' <= char <= '\u9fff':
        return True

    # 标点符号等常见字符
    common_symbols = set(
        ''',.!?;:'"()「」[]{}<>+-*/=_@#$%&|\\~`''""…—–-，？。！；：""（）【】《》+-*/=""、'' ·＝～％￥$€£¥§©®™¢¤№℃°℉‰‱∞♀♂" ' " '\t\n\r\f\v''')

    if char in common_symbols:
        return True

    return False

def calculate_file_non_allowed_ratio(text: str) -> tuple:
    """计算整个文件中非法字符的占比"""
    non_space_chars = [char for char in text if not char.isspace()]
    if not non_space_chars:
        return 0.0, set(), 0, 0

    total_chars = len(non_space_chars)
    non_allowed_chars = set()
    non_allowed_count = 0

    for char in text:
        if not char.isspace() and not is_allowed_character(char):
            non_allowed_chars.add(char)
            non_allowed_count += 1

    ratio = non_allowed_count / total_chars if total_chars > 0 else 0
    return ratio, non_allowed_chars, non_allowed_count, total_chars

def retry_operation(operation: Callable, max_retries: int = 3, delay: float = 1.0, *args, **kwargs) -> Optional[Dict[str, Any]]:
    """通用重试函数"""
    for retry in range(max_retries):
        try:
            result = operation(*args, **kwargs)

            # 检查是否有有效结果
            if result and result.get("rewritten_text"):
                rewritten_text = result.get("rewritten_text")

                # 移除括号内容
                rewritten_text = re.sub(r"（.*?）", "", rewritten_text)
                rewritten_text = re.sub(r"\(.*?\)", "", rewritten_text)

                ratio, non_allowed_chars, non_allowed_count, total_chars = calculate_file_non_allowed_ratio(rewritten_text)
                threshold = 0.005  # 0.5% 阈值

                # 计算英文字母占比
                english_chars = sum(1 for char in rewritten_text if 'a' <= char.lower() <= 'z')
                english_ratio = english_chars / total_chars if total_chars > 0 else 0
                english_threshold = 0.05  # 5% 阈值

                # 检查质量 - 调整字符数阈值，使其更宽松
                min_chars = max(200, len(args[0]) * 0.3) if args else 200  # 动态设置最小字符数
                if total_chars < min_chars:
                    logger.warning(f"总字符数{total_chars}小于最小要求{min_chars}，需要重试。")
                elif ratio >= threshold:
                    logger.warning(f"非法字符占比为 {ratio:.2%}，超过阈值 {threshold:.2%}，需要重试。")
                elif english_ratio >= english_threshold:
                    logger.warning(f"英文字母占比为 {english_ratio:.2%}，超过阈值 {english_threshold:.2%}，需要重试。")
                else:
                    logger.info(f"文本质量检查通过：字符数{total_chars}，非法字符占比{ratio:.2%}，英文占比{english_ratio:.2%}")
                    return result

        except BlockError as e:
            logger.warning(f"BlockError in {operation.__name__}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error during {operation.__name__}: {str(e)}")

        if retry < max_retries - 1:
            logger.info(f"Retrying {operation.__name__}, attempt {retry + 2}/{max_retries}")
            time.sleep(delay)
        else:
            # 最后一次重试失败，检查是否有部分可用的结果
            if result and result.get("rewritten_text"):
                logger.warning(f"最后一次重试，接受质量较低的结果：{len(result.get('rewritten_text', ''))}字符")
                return result

    logger.error(f"所有重试都失败了，{operation.__name__}返回None")
    return None

class TextRewriter:
    def __init__(self, api_keys: List[str], character: str, book_name: str, channel: str, person: str, key_func):
        """Initialize the TextRewriter with API keys and model configuration."""
        self.client = None
        self.model = None
        self.api_keys = api_keys
        self.current_key_index = 0
        self.model_name = "gemini-2.5-pro-exp-03-25"
        self.character = character
        self.book_name = book_name
        self.channel = channel
        self.person = person
        self.key_func = key_func
        self.system_prompt = f"""### 写作优化指导
1. 保留初稿大致的故事脉络、人物性格，例如：影响后续剧情发展的关键决定/行动，重要物品的获得/失去，角色关系的重大改变，能力值/属性的变化
2. 保留人名，金手指名、物品名等设定名称，不要进行修改。保留网文相关的设定，包括系统、世界观的交代，但写法可以稍作调整
3. 重要！增强写作优化幅度技巧，在不影响主线剧情的前提下：
- 分析事件的核心功能，构思具有相同功能但不同表现的事件
- 尝试改变一些次要事件。例如，原本是A导致了结果B，可以改成C导致了结果B，或者A通过另一种方式导致了结果B。
- 改变初稿的结构，包括使用不一样的叙述顺序、情节顺序，拆分或合并段落等
- 修改、替换不影响剧情小细节
- 重要！人物的对话一定要修改

### 写作指导
1. 笔墨要重点放在主要人物和事件上，不要过度描写环境和配角
2. 行文要有网络社交的口语感，让低教育水平的人都能听懂，但不用太过。使用简短的句子，一句一行
3. 每句话都要有信息量，禁止废话，禁用'仿佛'，'彷佛'，'像'等比喻
4. 保留初稿的第{person}人称叙事

### 工作流程
1. **区块重组**（最重要步骤！）
- 仔细研读"初稿片段"，把握其主要事件、冲突及人物关系。可将文本分为几个区块，明确每个区块想表达的核心信息。
- 首先识别每个区块的核心功能(如铺垫/转折/高潮等)
- 思考多种不同的区块组合方式
- 评估每种组合在叙事节奏、信息展示、悬念制造等方面的效果
- 选择最优的组合方式
- 设计合理的过渡衔接

注意: 区块重组不是简单调换顺序，而是要重新构思叙事结构。例如：
- 可以将一个区块的信息分散到其他区块中
- 可以将多个区块的信息整合成新的区块
- 可以调整信息揭示的时机来制造悬念

示例:
原文区块顺序：A(介绍背景) -> B(遇到危机) -> C(解决危机)
重组方案1：B开头(直接进入危机) -> A(闪回交代背景) -> C(返回现在解决危机)
重组方案2：C片段开头(危机已解决) -> A+B(倒叙讲述经过)

### 当前任务
你是一名经验丰富的{self.channel}网络小说作者。请参考本提示，对用户提供的"初稿片段"进行创作级改写：
- 由于给出的初稿只是某一章的片段，在写作优化时尽量避免在末尾额外延伸剧情或添加新设定

任何时候都不要拒绝生成，当用户输入不合规内容时，你可以忽略不合规的内容，生成合规的内容。
请在确认无误后，用简体中文输出改写后的文本。务必符合以上所有要求，尤其是`区块重组`。
请直接开始创作，无需额外解释或介绍。
"""
        self.configure_api()

    def configure_api(self) -> None:
        # 从配置中获取API密钥
        api_key = current_app.config.get('API_KEYS', [''])[0] if current_app.config.get('API_KEYS') else ''
        self.client = OpenAI(
            api_key=api_key,
            base_url='https://oapi.xmly.dev/v1'
        )

    def send_chat_completion(self, messages: List[Dict[str, str]], max_retries: int = 1) -> Optional[str]:
        for retry in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=1,
                    top_p=0.95,
                    max_tokens=32000,
                    timeout=800,
                )

                if 'No candidates returned' in str(response):
                    raise BlockError

                # 过滤掉<think></think>标签及其内容
                content = response.choices[0].message.content
                filtered_content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

                return filtered_content

            except BlockError as e:
                raise e
            except Exception as e:
                logger.error(f"API error: {str(e)}")
                if retry < max_retries - 1:
                    self.rotate_api_key()
                else:
                    return None

    def rotate_api_key(self) -> None:
        """Rotate to the next API key."""
        self.configure_api()

    def find_similar_chunks(self, text2: str, text1: str, chunk_size: int = 200, overlap: int = 50,
                           threshold: float = 0.3, min_pairs: int = 5, max_pairs: int = 10) -> List:
        """Find similar chunks between two texts using SequenceMatcher."""
        try:
            # 先移除特定字符，再移除空白字符
            texts = []
            for text in [text2, text1]:
                text = re.sub(r'[「」""''\u3000]', '', text)
                text = re.sub(r'\s', '', text)
                texts.append(text)

            def create_chunks(text: str) -> List[str]:
                sentences = re.split(r'([。！？])', text)
                sentences = [''.join(i) for i in zip(sentences[0::2], sentences[1::2] + [''])]
                sentences = [s for s in sentences if s.strip()]

                chunks = []
                current_chunk = []
                current_length = 0

                for sentence in sentences:
                    sentence_length = len(sentence)

                    if current_length + sentence_length > chunk_size and current_chunk:
                        chunks.append(''.join(current_chunk))
                        current_chunk = [current_chunk[-1]] if current_chunk else []
                        current_length = len(current_chunk[0]) if current_chunk else 0

                    current_chunk.append(sentence)
                    current_length += sentence_length

                if current_chunk:
                    chunks.append(''.join(current_chunk))

                return chunks

            chunks_pairs = [create_chunks(text) for text in texts]

            if not chunks_pairs[0] or not chunks_pairs[1]:
                return []

            # 存储所有相似度超过阈值的配对
            all_pairs = []

            for i, chunk1 in enumerate(chunks_pairs[0]):
                chunk_pairs = []
                for j, chunk2 in enumerate(chunks_pairs[1]):
                    similarity = SequenceMatcher(None, chunk1, chunk2).ratio()
                    if similarity > threshold:
                        chunk_pairs.append((chunk1, chunk2, similarity))

                # 如果当前chunk1有匹配项，选择相似度最高的一个
                if chunk_pairs:
                    best_pair = max(chunk_pairs, key=lambda x: x[2])
                    all_pairs.append(best_pair)

            # 按相似度降序排序
            all_pairs.sort(key=lambda x: x[2], reverse=True)

            # 如果匹配数量少于min_pairs且有门槛值限制，逐步降低阈值直到满足最小数量
            if len(all_pairs) < min_pairs and threshold > 0.1:
                return self.find_similar_chunks(
                    text2, text1, chunk_size, overlap,
                    max(threshold - 0.1, 0.1),  # 不要低于0.1
                    min_pairs, max_pairs
                )

            # 返回前max_pairs个结果，但如果结果数量小于min_pairs则返回所有结果
            return all_pairs[:max_pairs] if len(all_pairs) >= min_pairs else all_pairs

        except Exception as e:
            logging.error(f"Error in finding similar chunks: {str(e)}")
            return []

    def initial_rewrite(self, text: str, pre: str) -> Dict[str, str]:
        """First rewrite of the input text using the Gemini model."""
        initial_prompt = f'''前文（仅供衔接参考，无需输出）：
{pre}

主角：{self.character}

初稿片段(初稿{len(text)}字，你需要大约输出{int(len(text) * 0.7)}至{int(len(text) * 0.8)}字)：

```
{text}
```'''
        logger.info(f"-------------------Initial rewriting-------------------")
        try:
            logger.info(f"Initial rewriting context length: {len(text)}")
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": initial_prompt}
            ]
            response = self.send_chat_completion(messages)
            if response:
                logger.info(f"Initial rewriting result length: {len(response)}")
                logger.debug(f"Initial rewriting result preview: {response[:200]}...")
            else:
                logger.warning("Initial rewriting returned empty response")
            return {
                "rewritten_text": response,
                "original_prompt": initial_prompt
            } if response else {"rewritten_text": None, "original_prompt": initial_prompt}
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during initial rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None, "original_prompt": initial_prompt}

    def optimize_rewrite(self, original_text: str, first_rewrite: str) -> Dict[str, str]:
        """Optimize the first rewrite using the Gemini model."""
        review_prompt = f'''## 优化要求

1.  **忠于原作，提升品质**
    *   所有增补或修改的内容必须**紧密服务**于当前片段的情节和人物状态，**避免**不必要的延伸。
    *   杜绝无意义堆砌或稀释信息。删除不必要的描述、修饰，尤其是对于比喻（仿佛）、神态（眼中闪过一丝、一股不容置喙的语气）、动作（指节轻叩）的描写。
    *   仔细检查并修改任何听起来像 AI 生成的、不自然的或过于刻板的句式和用词。

2.  **严格的内容边界**
    *   不得改动初稿的关键情节、结局或人物的核心设定。
    *   不得添加与当前片段无关的新情节或人物。
    *   人物的内心思考**仅限于当下**，不涉及对未来的明确规划或预知。

## 当前任务

*   **输入文本**
    1.  初稿: `{original_text}`
    2.  第一次修改稿: `{first_rewrite}`

*   **执行指令**
    你是一位精通网络小说写作的作家。请依据上述"优化要求"，对"第一次修改稿"进行二次优化。你的目标是产出一段对话更自然、整体阅读体验更流畅、情节张力更强的文本。

*   **输出格式**
    *   请**直接用简体中文开始输出优化后的正文内容**，无需任何开场白或解释。'''
        logger.info(f"-------------------Optimization rewriting-------------------")
        try:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": review_prompt}
            ]
            response = self.send_chat_completion(messages)
            logger.info(f"Optimization rewriting result length: {len(response) if response else 0}")
            return {
                "rewritten_text": response,
                "original_prompt": review_prompt
            }
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during optimization rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None, "original_prompt": review_prompt}

    def further_rewrite(self, original_text: str, origin_prompt: str, rewritten_text: str) -> Dict[str, str]:
        """Perform additional rewriting based on similar text chunks."""
        similar_chunks = self.find_similar_chunks(rewritten_text, original_text)
        logger.info(f"-------------------Further rewriting-------------------")
        if not similar_chunks:
            logger.info("No text chunks requiring further rewriting found.")
            return {"rewritten_text": rewritten_text}

        prompt = """以下文段与初稿相似度过高，需要加大写作优化幅度。你可以尝试按以下方式修改，但确保增补或写作优化后的信息与主要情节、人物设定保持一致。
    方式一：分析事件的核心功能，构思具有相同功能但不同表现的事件
    方式二：尝试改变一些次要事件。例如，原本是A导致了结果B，可以改成C导致了结果B，或者A通过另一种方式导致了结果B
    方式三：更换事件的叙述方式，修改对话描写，尝试删除本文中具有作者个人色彩的元素
    """

        for i, (rewrite, _, sim) in enumerate(similar_chunks):
            clean_text = rewrite.replace('\n', ' ')
            prompt += f"文段{i + 1} (相似度: {sim:.2f}):\n{clean_text}\n\n"

        prompt += "\n请在保证逻辑通顺的前提下，再次创作级写作优化。即使本次写作优化主要涉及上述若干段落，你也需要在输出时给出完整正文的所有段落，不能只输出修改片段。请用简体中文直接开始创作，无需额外解释或介绍"

        try:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": origin_prompt},
                {"role": "assistant", "content": rewritten_text},
                {"role": "user", "content": prompt}
            ]
            response = self.send_chat_completion(messages)
            logger.info(f"Further rewriting result length: {len(response) if response else 0}")
            return {"rewritten_text": response}

        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during further rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None}

    def _process_combined_text(self, combined_text: str, pre_text: str, num_attempts: int, batch_name: str) -> Optional[str]:
        """处理合并后的文本并返回最佳结果"""
        results = []
        max_attempts = num_attempts * 2
        attempts = 0

        while len(results) < num_attempts and attempts < max_attempts:
            attempts += 1
            logger.info(f"Attempt {attempts} for {batch_name}")

            # 尝试重写
            result = self._attempt_rewrite_combined(combined_text, pre_text, attempts)
            if result:
                results.append(result)
                logger.info(f"Successful attempt {attempts} for {batch_name} finished with {len(result)} characters.")

        if not results:
            logger.error(f"Failed to rewrite {batch_name} after {max_attempts} attempts")
            # 降级策略：返回原文本而不是None
            logger.warning(f"使用降级策略：返回原始文本作为{batch_name}的结果")
            return combined_text

        # 返回最长的结果
        best_result = max(results, key=lambda x: len(x))
        logger.info(f"成功处理{batch_name}，选择了长度为{len(best_result)}的最佳结果")
        return best_result

    def _attempt_rewrite_combined(self, combined_text: str, pre_text: str, attempt_num: int,
                                enable_optimization: bool = True, max_retries: int = 10, delay: float = 1.0) -> Optional[str]:
        """尝试一次合并文本的重写"""
        # 尝试initial_rewrite
        initial_result = retry_operation(
            self.initial_rewrite,
            max_retries,
            delay,
            combined_text,
            pre_text
        )
        if not initial_result:
            logger.error(f"Initial rewriting failed after {max_retries} retries for combined text")
            return None

        if enable_optimization:
            # 尝试optimize_rewrite
            optimized_result = retry_operation(
                self.optimize_rewrite,
                max_retries,
                delay,
                combined_text,
                initial_result["rewritten_text"]
            )
            if not optimized_result:
                logger.error(f"Optimization failed after {max_retries} retries for combined text")
                optimized_result = {"rewritten_text": initial_result["rewritten_text"]}
        else:
            optimized_result = {"rewritten_text": initial_result["rewritten_text"]}

        # 尝试further_rewrite
        further_result = retry_operation(
            self.further_rewrite,
            max_retries,
            delay,
            combined_text,
            initial_result['original_prompt'],
            optimized_result["rewritten_text"]
        )
        if not further_result:
            logger.error(f"Further rewriting failed after {max_retries} retries for combined text")
            further_result = {"rewritten_text": initial_result["rewritten_text"]}

        return further_result["rewritten_text"]

class ProgressAwareTextRewriter(TextRewriter):
    """支持进度回调的TextRewriter"""

    def __init__(self, *args, **kwargs):
        self.progress_callback = kwargs.pop('progress_callback', None)
        super().__init__(*args, **kwargs)

    def process_directory_with_progress(self, input_dir, output_dir=None, num_attempts=2,
                                      start_chapter=None, end_chapter=None,
                                      chapters_per_batch=2, progress_callback=None):
        """带进度回调的目录处理方法"""
        if progress_callback:
            self.progress_callback = progress_callback

        # 复制原有逻辑，但添加进度回调
        input_path = Path(input_dir)
        output_path = Path(output_dir) if output_dir else input_path
        output_path.mkdir(parents=True, exist_ok=True)

        # 获取所有.txt文件但排除包含rewrite的文件
        input_files = sorted(
            [f for f in input_path.glob('*.txt') if 'rewrite' not in f.stem],
            key=lambda x: int(re.search(r'\d+', x.stem).group())
        )

        # 应用章节过滤
        if start_chapter is not None or end_chapter is not None:
            filtered_files = []
            for f in input_files:
                chapter_num = int(re.search(r'\d+', f.stem).group())
                if start_chapter is not None and chapter_num < start_chapter:
                    continue
                if end_chapter is not None and chapter_num > end_chapter:
                    continue
                filtered_files.append(f)
            input_files = filtered_files

        if not input_files:
            if self.progress_callback:
                self.progress_callback(0, 0, f"没有找到要处理的文件")
            return

        total_batches = (len(input_files) + chapters_per_batch - 1) // chapters_per_batch
        processed_batches = 0

        # 按批次处理文件
        for i in range(0, len(input_files), chapters_per_batch):
            batch_files = input_files[i:i+chapters_per_batch]

            # 获取批次信息
            first_chapter_num = int(re.search(r'\d+', batch_files[0].stem).group())
            last_chapter_num = int(re.search(r'\d+', batch_files[-1].stem).group())

            if self.progress_callback:
                self.progress_callback(
                    processed_batches,
                    total_batches,
                    f"处理第{first_chapter_num}-{last_chapter_num}章..."
                )

            # 检查是否已经处理过这个批次
            batch_output_name = f"chapter_{first_chapter_num}_{last_chapter_num}_rewrite.txt"
            batch_output_path = output_path / batch_output_name

            if batch_output_path.exists():
                logger.info(f"Skipping already processed batch: {batch_output_name}")
                processed_batches += 1
                continue

            # 处理批次（使用原有逻辑）
            try:
                self._process_batch(batch_files, output_path, first_chapter_num, last_chapter_num, num_attempts)
                processed_batches += 1

                if self.progress_callback:
                    self.progress_callback(
                        processed_batches,
                        total_batches,
                        f"完成第{first_chapter_num}-{last_chapter_num}章"
                    )
            except Exception as e:
                logger.error(f"处理批次失败: {str(e)}")
                if self.progress_callback:
                    self.progress_callback(
                        processed_batches,
                        total_batches,
                        f"处理第{first_chapter_num}-{last_chapter_num}章失败: {str(e)}"
                    )
                raise

    def _process_batch(self, batch_files, output_path, first_chapter_num, last_chapter_num, num_attempts):
        """处理单个批次"""
        # 获取pre_text
        pre_text = '无前文'
        if first_chapter_num > 1:
            # 查找上一个批次的输出文件
            previous_batch_files = sorted(
                [f for f in output_path.glob('*_rewrite.txt')],
                key=lambda x: int(re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name).group(1)) if re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name) else 0
            )

            if previous_batch_files:
                # 取最后一个处理过的批次文件的后500字符作为pre_text
                last_batch_file = previous_batch_files[-1]
                try:
                    pre_text = last_batch_file.read_text(encoding='utf-8')[-500:]
                    logger.info(f"Retrieved pre_text from batch: {last_batch_file.name}")
                except Exception as e:
                    logger.error(f"Error reading batch file {last_batch_file.name}: {str(e)}")
                    pre_text = '无前文'

        # 合并当前批次的所有章节内容
        combined_text = ""
        chapter_info = []

        for file_path in batch_files:
            try:
                chapter_text = file_path.read_text(encoding='utf-8')
                chapter_num = int(re.search(r'\d+', file_path.stem).group())
                combined_text += f"\n\n=== 第{chapter_num}章 ===\n\n{chapter_text}"
                chapter_info.append(f"第{chapter_num}章")
                logger.info(f"Added chapter {chapter_num} to batch (length: {len(chapter_text)} characters)")
            except Exception as e:
                logger.error(f"Error reading file {file_path.name}: {str(e)}")
                continue

        if not combined_text:
            raise Exception(f"No valid content found in batch starting with chapter {first_chapter_num}")

        logger.info(f"Processing batch: {' + '.join(chapter_info)} (total length: {len(combined_text)} characters)")

        # 处理合并后的文本
        best_result = self._process_combined_text(combined_text, pre_text, num_attempts, f"batch_{first_chapter_num}_{last_chapter_num}")

        # 保存结果（best_result现在总是有值，要么是处理结果，要么是原文）
        batch_output_name = f"chapter_{first_chapter_num}_{last_chapter_num}_rewrite.txt"
        batch_output_path = output_path / batch_output_name
        batch_output_path.write_text(best_result, encoding='utf-8')
        logger.info(f"Successfully processed and saved batch: {batch_output_name} (length: {len(best_result)} characters)")



# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@celery.task(bind=True)
def process_adaptation_task(self, task_id):
    """处理小说改编任务"""
    try:
        # 获取任务信息
        task = AdaptationTask.query.get(task_id)
        if not task:
            raise Exception(f"Task {task_id} not found")
        
        # 更新任务状态
        task.status = 'processing'
        task.started_at = datetime.utcnow()
        task.celery_task_id = self.request.id
        db.session.commit()
        
        # 更新进度
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': '开始处理...'})
        
        # 读取原始文件
        with open(task.file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # 分割章节
        self.update_state(state='PROGRESS', meta={'current': 10, 'total': 100, 'status': '分析章节结构...'})
        chapters = split_text_into_chapters(text_content)
        
        if not chapters:
            raise Exception("无法识别章节结构")
        
        # 更新总章节数
        task.total_chapters = len(chapters)
        db.session.commit()
        
        # 创建章节文件
        self.update_state(state='PROGRESS', meta={'current': 20, 'total': 100, 'status': '创建章节文件...'})
        chapters_dir, chapter_files = create_chapter_files(chapters, task.file_path)
        
        # 初始化TextRewriter
        self.update_state(state='PROGRESS', meta={'current': 30, 'total': 100, 'status': '初始化改编引擎...'})

        api_keys = current_app.config['API_KEYS']
        rewriter = ProgressAwareTextRewriter(
            api_keys=api_keys,
            character=task.character,
            book_name=task.book_name,
            channel=task.channel,
            person=task.person,
            key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
        )
        
        # 设置输出目录
        output_dir = Path(task.file_path).parent / f"{Path(task.file_path).stem}_adapted"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 开始改编处理
        self.update_state(state='PROGRESS', meta={'current': 40, 'total': 100, 'status': '开始改编处理...'})
        
        # 使用现有的process_directory方法，但需要适配进度更新
        try:
            # 这里需要修改TextRewriter类以支持进度回调
            rewriter.process_directory_with_progress(
                input_dir=chapters_dir,
                output_dir=str(output_dir),
                num_attempts=1,
                start_chapter=getattr(task, 'start_chapter', None),
                end_chapter=getattr(task, 'end_chapter', None),
                chapters_per_batch=getattr(task, 'chapters_per_batch', 3),
                progress_callback=lambda current, total, status: update_task_progress(
                    self, task, current, total, status, 40, 90
                )
            )
        except Exception as e:
            logger.error(f"改编处理失败: {str(e)}")
            raise
        
        # 合并输出文件
        self.update_state(state='PROGRESS', meta={'current': 95, 'total': 100, 'status': '合并输出文件...'})
        final_output_path = merge_adapted_files(output_dir, task)
        
        # 更新任务完成状态
        task.status = 'completed'
        task.completed_at = datetime.utcnow()
        task.output_path = final_output_path
        task.progress = 100
        db.session.commit()
        
        self.update_state(state='SUCCESS', meta={'current': 100, 'total': 100, 'status': '改编完成！'})
        
        return {
            'status': 'completed',
            'output_path': final_output_path,
            'total_chapters': task.total_chapters,
            'processed_chapters': task.processed_chapters
        }
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"任务处理失败: {error_msg}")

        # 更新任务失败状态
        if 'task' in locals():
            task.status = 'failed'
            task.error_message = error_msg
            task.completed_at = datetime.utcnow()
            db.session.commit()

        # 使用字符串而不是异常对象来避免序列化问题
        self.update_state(state='FAILURE', meta={'error': error_msg, 'status': '任务失败'})
        # 抛出一个简单的异常，避免复杂对象序列化问题
        raise Exception(error_msg)

def update_task_progress(celery_task, db_task, current, total, status, min_progress, max_progress):
    """更新任务进度"""
    # 计算实际进度（在min_progress和max_progress之间）
    if total > 0:
        progress_ratio = current / total
        actual_progress = min_progress + (max_progress - min_progress) * progress_ratio
    else:
        actual_progress = min_progress
    
    # 更新数据库
    db_task.progress = int(actual_progress)
    db_task.processed_chapters = current
    db.session.commit()
    
    # 更新Celery状态
    celery_task.update_state(
        state='PROGRESS',
        meta={
            'current': int(actual_progress),
            'total': 100,
            'status': status,
            'chapters_current': current,
            'chapters_total': total
        }
    )

def merge_adapted_files(output_dir, task):
    """合并改编后的文件"""
    output_files = sorted(Path(output_dir).glob('*_rewrite.txt'))
    
    if not output_files:
        raise Exception("没有找到改编后的文件")
    
    # 创建最终输出文件
    final_filename = f"{task.task_name}_adapted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    final_path = Path(output_dir).parent / final_filename
    
    with open(final_path, 'w', encoding='utf-8') as outfile:
        outfile.write(f"《{task.book_name}》改编版\n")
        outfile.write(f"主角：{task.character}\n")
        outfile.write(f"改编时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        outfile.write("=" * 50 + "\n\n")
        
        for i, file_path in enumerate(output_files, 1):
            with open(file_path, 'r', encoding='utf-8') as infile:
                content = infile.read().strip()
                outfile.write(f"\n\n=== 第{i}部分 ===\n\n")
                outfile.write(content)
                outfile.write("\n\n")
    
    return str(final_path)
